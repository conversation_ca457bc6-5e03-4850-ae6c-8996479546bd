# 时间选择器改造说明

## 概述
将 `src/components/Chat/MessageComponent/ConfigForm.tsx` 中的 `TimeRangePickerField` 组件从原生 Modal 实现改为基于 roo-rn 的 `Datepicker` 和 `SlideModal` 组件实现。

## 主要修改

### 1. 导入更新
```typescript
// 原来
import { Icon, Switch } from '@roo/roo-rn';

// 修改后
import { Icon, Switch, SlideModal, Datepicker } from '@roo/roo-rn';
import React, { useContext, useEffect, useState, useRef } from 'react';
```

### 2. 组件状态重构
```typescript
// 原来：使用字符串存储时间
const [startTime, setStartTime] = useState('09:00');
const [endTime, setEndTime] = useState('18:00');

// 修改后：使用 Date 对象存储时间
const [startTime, setStartTime] = useState(new Date());
const [endTime, setEndTime] = useState(new Date());
const [currentPicker, setCurrentPicker] = useState<'start' | 'end'>('start');
const startPickerRef = useRef<any>(null);
const endPickerRef = useRef<any>(null);
```

### 3. UI 结构改造
- **原来**：使用原生 `Modal` + 自定义滚动列表
- **修改后**：使用 roo-rn 的 `SlideModal` + `Datepicker` 组件

### 4. 新增功能
- 添加了开始时间和结束时间的切换标签页
- 使用 roo-rn 的原生时间选择器，提供更好的用户体验
- 支持通过 ref 获取选中的时间值

### 5. 样式更新
```typescript
// 新增样式
timePickerTabs: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingHorizontal: 16,
},
timePickerTab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F5F6FA',
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
},
timePickerTabActive: {
    backgroundColor: '#6C5CE7',
},
datePickerContainer: {
    backgroundColor: '#FFFFFF',
    minHeight: 200,
},
```

## 技术特点

### 1. 基于 roo-rn 组件库
- 使用 `SlideModal` 提供统一的弹窗体验
- 使用 `Datepicker` 提供原生的时间选择体验
- 保持与项目其他组件的一致性

### 2. 改进的用户体验
- 标签页切换：用户可以清楚地看到当前选择的是开始时间还是结束时间
- 实时预览：标签页显示当前选中的时间值
- 原生滚轮：使用 roo-rn 的 Datepicker 提供更流畅的滚轮选择体验

### 3. 代码结构优化
- 使用 ref 管理 Datepicker 实例
- 统一的时间格式化函数
- 清晰的状态管理

## 使用方式

组件的外部接口保持不变，仍然通过以下方式使用：

```typescript
<TimeRangePickerField
    label="工作时间"
    value="09:00-18:00"
    onChange={(value) => handleChange('workTime', value)}
    disabled={false}
/>
```

## 兼容性

- 保持了原有的 API 接口不变
- 输入输出格式保持一致（"HH:mm-HH:mm" 格式）
- 向后兼容现有的使用方式

## 测试建议

1. 测试时间选择功能是否正常工作
2. 测试开始时间和结束时间的切换
3. 测试确定和取消按钮的功能
4. 测试禁用状态的表现
5. 测试默认值的解析和显示
