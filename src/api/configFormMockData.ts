/**
 * ConfigFormMessage 专用 Mock 数据
 * 提供各种场景下的 ConfigForm 组件测试数据
 */

import { ConfigFormMessage } from '../types/message';

/**
 * 基础的拜访签入提醒配置（展示所有控件类型）
 */
export const visitReminderConfig: ConfigFormMessage = {
    type: 'configForm',
    insert: {
        configForm: {
            config: [
                {
                    label: '开启提醒',
                    labelStyle: 'bold',
                    type: 'switch',
                    defaultValue: 'true',
                },
                {
                    label: '提醒频次',
                    type: 'select',
                    options: ['每天', '每周', '每月', '从不'],
                    defaultValue: '每天',
                },
                {
                    label: '提醒时间',
                    type: 'timeRangePicker',
                    defaultValue: '13:00-18:00',
                },
            ],
            formId: 'signReminder',
            buttonText: '保存',
        },
    },
};

/**
 * 完整的通知设置配置（包含所有控件类型）
 */
export const fullNotificationConfig: ConfigFormMessage = {
    type: 'configForm',
    insert: {
        configForm: {
            config: [
                {
                    label: '开启推送通知',
                    labelStyle: 'bold',
                    type: 'switch',
                    defaultValue: 'true',
                },
                {
                    label: '通知类型',
                    type: 'select',
                    options: ['推送通知', '短信通知', '邮件通知', '微信通知'],
                    defaultValue: '推送通知',
                },
                {
                    label: '免打扰时间',
                    type: 'timeRangePicker',
                    defaultValue: '22:00-08:00',
                },
                {
                    label: '声音提醒',
                    type: 'switch',
                    defaultValue: 'false',
                },
                {
                    label: '震动提醒',
                    labelStyle: 'bold',
                    type: 'switch',
                    defaultValue: 'true',
                },
            ],
            formId: 'notificationSettings',
            buttonText: '确定',
        },
    },
};

/**
 * 工作时间设置配置
 */
export const workTimeConfig: ConfigFormMessage = {
    type: 'configForm',
    insert: {
        configForm: {
            config: [
                {
                    label: '启用工作时间限制',
                    labelStyle: 'bold',
                    type: 'switch',
                    defaultValue: 'false',
                },
                {
                    label: '工作日设置',
                    type: 'select',
                    options: ['周一至周五', '周一至周六', '周一至周日', '自定义'],
                    defaultValue: '周一至周五',
                },
                {
                    label: '上班时间',
                    type: 'timeRangePicker',
                    defaultValue: '09:00-18:00',
                },
                {
                    label: '午休时间',
                    type: 'timeRangePicker',
                    defaultValue: '12:00-13:00',
                },
            ],
            formId: 'workTimeSettings',
            buttonText: '应用设置',
        },
    },
};

/**
 * 数据同步设置配置
 */
export const dataSyncConfig: ConfigFormMessage = {
    type: 'configForm',
    insert: {
        configForm: {
            config: [
                {
                    label: '自动同步数据',
                    labelStyle: 'bold',
                    type: 'switch',
                    defaultValue: 'true',
                },
                {
                    label: '同步频率',
                    type: 'select',
                    options: ['实时同步', '每5分钟', '每小时', '每天', '手动同步'],
                    defaultValue: '每小时',
                },
                {
                    label: '同步时间窗口',
                    type: 'timeRangePicker',
                    defaultValue: '06:00-22:00',
                },
                {
                    label: '仅WiFi同步',
                    type: 'switch',
                    defaultValue: 'false',
                },
            ],
            formId: 'dataSyncSettings',
            buttonText: '保存配置',
        },
    },
};

/**
 * 隐私设置配置
 */
export const privacyConfig: ConfigFormMessage = {
    type: 'configForm',
    insert: {
        configForm: {
            config: [
                {
                    label: '位置信息共享',
                    labelStyle: 'bold',
                    type: 'switch',
                    defaultValue: 'true',
                },
                {
                    label: '数据分析参与',
                    type: 'switch',
                    defaultValue: 'false',
                },
                {
                    label: '隐私级别',
                    type: 'select',
                    options: ['公开', '朋友可见', '仅自己', '完全隐私'],
                    defaultValue: '朋友可见',
                },
            ],
            formId: 'privacySettings',
            buttonText: '更新隐私设置',
        },
    },
};

/**
 * 简单的开关配置（最小化示例）
 */
export const simpleToggleConfig: ConfigFormMessage = {
    type: 'configForm',
    insert: {
        configForm: {
            config: [
                {
                    label: '启用功能',
                    type: 'switch',
                    defaultValue: 'false',
                },
            ],
            formId: 'simpleToggle',
        },
    },
};

/**
 * 所有预定义的 ConfigForm 配置
 */
export const allConfigFormExamples = {
    visitReminder: visitReminderConfig,
    fullNotification: fullNotificationConfig,
    workTime: workTimeConfig,
    dataSync: dataSyncConfig,
    privacy: privacyConfig,
    simpleToggle: simpleToggleConfig,
};

/**
 * 获取随机的 ConfigForm 配置
 */
export const getRandomConfigForm = (): ConfigFormMessage => {
    const configs = Object.values(allConfigFormExamples);
    const randomIndex = Math.floor(Math.random() * configs.length);
    return configs[randomIndex];
};

/**
 * 创建完整的聊天消息格式
 */
export const createConfigFormMessage = (
    config: ConfigFormMessage = visitReminderConfig,
    options: {
        msgId?: string;
        history?: boolean;
        status?: number;
    } = {}
) => {
    return {
        msgId: options.msgId || `config-form-${Date.now()}`,
        msgType: 1,
        status: options.status || 1,
        currentContent: [config],
        history: options.history || false,
        timestamp: Date.now(),
    };
};

/**
 * 生成测试用的多个 ConfigForm 消息
 */
export const generateConfigFormTestMessages = () => {
    return Object.entries(allConfigFormExamples).map(([key, config], index) => 
        createConfigFormMessage(config, {
            msgId: `test-config-${key}-${index}`,
            history: index % 2 === 1, // 交替设置历史状态
        })
    );
};
