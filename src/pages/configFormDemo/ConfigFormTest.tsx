import React from 'react';
import { View, ScrollView, Text, StyleSheet } from '@mrn/react-native';

import { ConfigForm } from '../../components/Chat/MessageComponent/ConfigForm';
import {
    visitReminderConfig,
    fullNotificationConfig,
    workTimeConfig,
    dataSyncConfig,
    privacyConfig,
    simpleToggleConfig,
} from '../../api/configFormMockData';

/**
 * ConfigForm 组件测试页面
 * 专门用于测试新的 ActionSheet 和 TimePicker 功能
 */
const ConfigFormTest: React.FC = () => {
    return (
        <ScrollView style={styles.container}>
            <Text style={styles.pageTitle}>ConfigForm 新功能测试</Text>
            <Text style={styles.subtitle}>ActionSheet 选择器 + 时间范围选择器</Text>
            
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>1. 拜访签入提醒（完整功能）</Text>
                <Text style={styles.description}>
                    包含：开关 + ActionSheet选择器 + 时间范围选择器
                </Text>
                <View style={styles.formWrapper}>
                    <ConfigForm {...visitReminderConfig.insert.configForm} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>2. 通知设置（多个开关）</Text>
                <Text style={styles.description}>
                    包含：多个开关 + ActionSheet选择器 + 时间范围选择器
                </Text>
                <View style={styles.formWrapper}>
                    <ConfigForm {...fullNotificationConfig.insert.configForm} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>3. 工作时间设置</Text>
                <Text style={styles.description}>
                    包含：开关 + ActionSheet选择器 + 多个时间范围选择器
                </Text>
                <View style={styles.formWrapper}>
                    <ConfigForm {...workTimeConfig.insert.configForm} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>4. 数据同步设置</Text>
                <Text style={styles.description}>
                    包含：开关 + ActionSheet选择器 + 时间范围选择器
                </Text>
                <View style={styles.formWrapper}>
                    <ConfigForm {...dataSyncConfig.insert.configForm} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>5. 隐私设置</Text>
                <Text style={styles.description}>
                    包含：多个开关 + ActionSheet选择器
                </Text>
                <View style={styles.formWrapper}>
                    <ConfigForm {...privacyConfig.insert.configForm} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>6. 简单开关（最小示例）</Text>
                <Text style={styles.description}>
                    仅包含：单个开关
                </Text>
                <View style={styles.formWrapper}>
                    <ConfigForm {...simpleToggleConfig.insert.configForm} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>7. 历史状态（已提交）</Text>
                <Text style={styles.description}>
                    所有控件禁用，按钮隐藏
                </Text>
                <View style={styles.formWrapper}>
                    <ConfigForm {...visitReminderConfig.insert.configForm} history={true} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>功能特性说明</Text>
                <View style={styles.featureList}>
                    <Text style={styles.featureItem}>✅ ActionSheet 风格的选择器（底部弹出）</Text>
                    <Text style={styles.featureItem}>✅ 时间范围选择器（开始时间 + 结束时间）</Text>
                    <Text style={styles.featureItem}>✅ 原生 Switch 开关组件</Text>
                    <Text style={styles.featureItem}>✅ 支持粗体标签样式</Text>
                    <Text style={styles.featureItem}>✅ 历史消息状态禁用</Text>
                    <Text style={styles.featureItem}>✅ JSON 格式数据提交</Text>
                    <Text style={styles.featureItem}>✅ 自定义按钮文案</Text>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F5F6FA',
    },
    pageTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#222222',
        textAlign: 'center',
        marginVertical: 20,
    },
    subtitle: {
        fontSize: 16,
        color: '#666666',
        textAlign: 'center',
        marginBottom: 20,
    },
    section: {
        marginBottom: 24,
        paddingHorizontal: 16,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#222222',
        marginBottom: 8,
    },
    description: {
        fontSize: 14,
        color: '#666666',
        marginBottom: 12,
        lineHeight: 20,
    },
    formWrapper: {
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 4,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    featureList: {
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    featureItem: {
        fontSize: 14,
        color: '#222222',
        lineHeight: 24,
        marginBottom: 4,
    },
});

export default ConfigFormTest;
