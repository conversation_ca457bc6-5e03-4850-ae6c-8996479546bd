export const mockServiceData = {
    // 排序表格
    sortTable: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '87027',
            msgId: '87031',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent:
                '[{"type":"markdown","insert":{"markdown":{"text":"### 新签攻克绩效综述：\\n- BD名称：余海龙\\n- 月新签模块得分：18.75%\\n- 目标新商家运营绩效得分：100%\\n- 普通新签目标完成率：18.75%（3 / 16）\\n\\n如果将以下 **_5_** 个商家全部攻克下来，您将预计提升 **_34.25%_** 绩效分（数据仅供参考，实际绩效以最终结果为准），其中上月追溯未攻克可以提高 **_14.00%_** 分，本月未攻克可以提高 **_20.25%_** 分。\\n\\n### 待攻克商家明细：\\n\\n#### 上月追溯未攻克商家推荐：\\n| 商家ID | 商家名称 | 新签追溯商家截止时间 | 剩余攻克时间 | 商家线索等级 | 上月追溯未攻克原因 | 距离攻克差值 | 攻克后质量得分 |\\n|--------|----------|----------------------|--------------|--------------|-------------------|--------------|----------------|\\n| [28137313](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=28137313) | 忆湘阁（虹桥店） | 20250708 | 1 | 1.0 | 月累计在线订单数 | 在线订单数差值4单 | 0.00% |\\n| [28446791](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=28446791) | 开心派对（简餐） | 20250724 | 17 | 2.0 | 月累计单均价（实付）≥15元 | 月均单均价差值1.11元 | 1.50% |\\n\\n#### 本月未攻克商家推荐：\\n| 商家ID | 商家名称 | 商家线索等级 | 当月未攻克原因 | 距离攻克差值 | 攻克后质量得分 |\\n|--------|----------|--------------|----------------|--------------|----------------|\\n| [27659465](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=27659465) | 三胖烧鸡 | 2.0 | 在线订单数低于标准 | 在线订单数差值1单 | 1.50% |\\n| [28586664](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=28586664) | 阿杰面馆(虹港北路店) | 1.0 | 单均&订单数 | 在线订单数差值8单<br>月均单均价差值1.69元 | 0.00% |\\n| [28663062](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=28663062) | 百丰面馆 | 1.0 | 营业时长&单均&订单数 | 在线订单数差值10单<br>月均单均价差值15.00元<br>商家营业时长差值24.53小时 | 0.00% |"}}},{"type":"markdown","insert":{"markdown":{"text":"\\n\\n\\n"}}},{"type":"markdown","insert":{"markdown":{"text":""}}},{"type":"markdown","insert":{"markdown":{"text":""}}},{"type":"markdown","insert":{"markdown":{"text":""}}},{"type":"actionCard","insert":{"actionCard":{"button":{"text":"去查询","action":"submitQuestion","type":"primary","question":"批量查询新店加权"},"title":"查新店加权","subTitle":"提升新商家曝光量和下单转化"}}},{"type":"suffixOptions","insert":{"suffixOptions":{"descriptions":"你还想问什么","options":[{"abilityType":1,"operationType":2,"content":"查商家是否开启新店加权"},{"abilityType":1,"operationType":2,"content":"诊断商家经营情况"},{"abilityType":1,"operationType":2,"content":"重新建店如何计算新签攻克绩效？"}]}}}]',
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1751875418321,
            tags: null,
        },
    },
    // 学城图片
    wikiPic: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5121310',
            msgId: '5121314',
            type: 2,
            abilityType: 1,
            status: 0,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请口为城市人员权限申请口为城市人员权限申请口为城市人员权限申请口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为：**先富系统-管理工具',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '-城市人员权限申请**，或直接访问 [权限申请入口](',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: 'https://igate.waimai.meituan.com/mfepro/organization/ApplyPermission/ApplyPermission',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '.html#/)。如页面提示无权限，需通过此入口申请。更多申请',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '规范可参考 [城市人员权限申请规范](',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: 'https://km.sankuai.com/page/164327767)。\n ![图片](https://km.sankuai.com/api/file/cdn/1349766872/3513148429xx?contentType=1&isNewContent=false&isNewContent=fals) \n![图片](https://s3plus.sankuai.com/bee-community/tmp_mrn_tmp_cda7eac8d8eb494b78eecaa090ed0e68.jpeg)\n ![图片](https://km.sankuai.com/api/file/cdn/1349766872/3513148429xxx?contentType=1&isNewContent=false&isNewContent=false)\n \n',
                            // "text": "https://km.sankuai.com/page/164327767)。\n \n![图片](https://msstest.sankuai.com/bdaiassistant-public/wiki_picture_4299630522_4487673508.png?AWSAccessKeyId=SRV_SKlNHJY4gJxvSlIIe3K6T2UwE4YYxI3h&Expires=1754645191&Signature=7qQJeASUdH5B2382dIULq%2FShUlo%3D\)\n \n"
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752052430689,
            tags: ['poiId'],
        },
    },

    // ActionCard 单按钮 - 外呼功能
    actionCardSingle: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135624',
            msgId: '5135634',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '基于您的商家数据分析，我们发现以下问题需要关注：',
                        },
                    },
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '串联次新运营绩效诊断',
                            subTitle:
                                '本月新签商家转化率较低，建议优化运营策略提升商家活跃度',
                            backgroundColor: '#f0f8ff',
                            button: {
                                text: '查看详细报告',
                                action: 'submitQuestion',
                                question:
                                    '请提供详细的串联次新运营绩效分析报告',
                                type: 'primary',
                                color: '#1890ff',
                            },
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // ActionCard 多按钮 - 外呼功能
    actionCardMultiple: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135625',
            msgId: '5135635',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '根据分析结果，为您提供以下操作选项：',
                        },
                    },
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '商家运营优化建议',
                            subTitle:
                                '基于数据分析，我们为您准备了多种优化方案',
                            backgroundColor: '#fff7e6',
                            buttonList: [
                                {
                                    text: '创建',
                                    action: 'openAICallModal',
                                    AICallParams: {
                                        taskName: '商家运营优化外呼',
                                        callScript:
                                            '您好，我们发现您的店铺运营数据有优化空间，想为您提供一些建议...',
                                        targetCount: 50,
                                    },
                                    type: 'normal',
                                },
                                {
                                    text: '导出数据',
                                    url: 'https://example.com/export-data',
                                    type: 'normal',
                                },
                            ],
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // PieChart 饼图数据
    pieChartData: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135626',
            msgId: '5135636',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '外呼任务执行结果统计如下：',
                        },
                    },
                },
                {
                    type: 'pieChart',
                    insert: {
                        pieChart: {
                            title: '外呼结果统计',
                            data: [
                                {
                                    label: '接通成功',
                                    value: 450,
                                    color: '#FFD100',
                                },
                                {
                                    label: '接通失败',
                                    value: 180,
                                    color: '#FF6B6B',
                                },
                                {
                                    label: '用户拒接',
                                    value: 120,
                                    color: '#FFD93D',
                                },
                                {
                                    label: '号码无效',
                                    value: 50,
                                    color: '#A8A8A8',
                                },
                            ],
                            size: 200,
                            showLegend: true,
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // AICallRecord 外呼任务记录 - 执行中
    AICallRecordRunning: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135627',
            msgId: '5135637',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '您的外呼任务正在执行中，当前进度如下：',
                        },
                    },
                },
                {
                    type: 'AICallRecord',
                    insert: {
                        AICallRecord: {
                            content: [
                                {
                                    jobName: '商家运营优化外呼任务',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '1000家' },
                                        {
                                            label: '创建时间',
                                            value: '2024-01-15 10:30:00',
                                        },
                                    ],
                                    button: {
                                        text: '查看详情',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705292200000,
                                    progress: 65,
                                },
                            ],
                            extendButtonName: '展开',
                            showNum: 1,
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // AICallRecord 外呼任务记录 - 已完成
    AICallRecordCompleted: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135628',
            msgId: '5135638',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '外呼任务已完成，执行结果如下：',
                        },
                    },
                },
                {
                    type: 'AICallRecord',
                    insert: {
                        AICallRecord: {
                            content: [
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                            ],
                            extendButtonName: '展开',
                            showNum: 2,
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // 综合场景：外呼任务完整流程
    aiCallCompleteFlow: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135630',
            msgId: '5135640',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '基于您的商家数据分析，我们为您提供完整的外呼解决方案：',
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '外呼任务管理中心',
                            subTitle: '一站式外呼任务创建、监控和分析平台',
                            backgroundColor: '#f6ffed',
                            buttonList: [
                                {
                                    text: '创建新任务',
                                    action: 'openAICallModal',
                                    AICallParams: {
                                        taskName: '商家激活外呼',
                                        callScript:
                                            '您好，恭喜您成功入驻美团外卖！我们想为您介绍一些运营技巧...',
                                        targetCount: 100,
                                    },
                                    type: 'primary',
                                },
                                {
                                    text: '查看历史',
                                    action: 'submitQuestion',
                                    question: '请显示我的外呼任务历史记录',
                                    type: 'normal',
                                },
                            ],
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n最近任务执行情况：',
                },
                {
                    type: 'AICallRecord',
                    insert: {
                        AICallRecord: {
                            content: [
                                {
                                    jobName: '本周商家关怀外呼',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '300家' },
                                        { label: '已完成', value: '234家' },
                                        { label: '成功接通', value: '180家' },
                                        { label: '失败', value: '54家' },
                                        {
                                            label: '创建时间',
                                            value: '2024.01.15 08:00:00',
                                        },
                                        {
                                            label: '最近更新',
                                            value: '2024.01.15 15:30:00',
                                        },
                                    ],
                                    button: {
                                        text: '查看进度',
                                        action: 'submitQuestion',
                                        question:
                                            '查看本周商家关怀外呼详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705283200000, // 2024-01-15 08:00:00
                                    progress: 78,
                                },
                                {
                                    jobName: '商家激活提醒外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '150家' },
                                        { label: '已完成', value: '150家' },
                                        { label: '成功接通', value: '120家' },
                                        {
                                            label: '创建时间',
                                            value: '2024.01.14 16:00:00',
                                        },
                                        {
                                            label: '完成时间',
                                            value: '2024.01.15 10:00:00',
                                        },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看商家激活提醒外呼结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705231200000, // 2024-01-14 16:00:00
                                    completeTime: 1705291200000, // 2024-01-15 10:00:00
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家欢迎外呼',
                                    status: 'init',
                                    descriptions: [
                                        { label: '外呼商家', value: '200家' },
                                        {
                                            label: '计划开始',
                                            value: '2024.01.16 09:00:00',
                                        },
                                        {
                                            label: '创建时间',
                                            value: '2024.01.15 17:00:00',
                                        },
                                    ],
                                    button: {
                                        text: '立即开始',
                                        action: 'submitQuestion',
                                        question:
                                            '立即开始新签商家欢迎外呼任务',
                                        type: 'primary',
                                    },
                                    createTime: 1705316400000, // 2024-01-15 17:00:00
                                },
                            ],
                            redirectButton: {
                                text: '查看结果',
                                action: 'submitQuestion',
                                question: '查看商家激活提醒外呼结果',
                                type: 'primary',
                            },
                            extendButtonName: '查看更多',
                            showNum: 2,
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n整体外呼效果分析：',
                },
                {
                    type: 'pieChart',
                    insert: {
                        pieChart: {
                            title: '本月外呼结果汇总',
                            data: [
                                {
                                    label: '成功接通',
                                    value: 1250,
                                    color: '#52c41a',
                                },
                                {
                                    label: '未接听',
                                    value: 680,
                                    color: '#faad14',
                                },
                                {
                                    label: '拒绝接听',
                                    value: 320,
                                    color: '#ff7a45',
                                },
                                {
                                    label: '号码异常',
                                    value: 150,
                                    color: '#d9d9d9',
                                },
                            ],
                            size: 220,
                            showLegend: true,
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },
    // 待签入商家列表
    toSignPoiList: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '6000001',
            msgId: '6000002',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'toSignPoiList',
                    insert: {
                        toSignPoiList: {
                            title: '请选择你想签到的商家',
                            reminderButtonText: '签入提醒设置',
                            reminderButtonQuestion: '设置签入提醒',
                            moreActionButtonText: '查看更多商家',
                            poiList: [
                                {
                                    avatar: 'https://s3plus.meituan.net/readata/sagacious_pigeon/ppt_static_img/report-logo.png',
                                    title: '赛百味·三明治（华彩店）',
                                    content: [
                                        {
                                            label: 'ID',
                                            value: '27891901',
                                        },
                                        {
                                            value: '距离120m',
                                        },
                                    ],
                                    actionButtonText: '签入',
                                    checkInTimestamp: undefined,
                                },
                                {
                                    avatar: 'https://s3plus.meituan.net/readata/sagacious_pigeon/ppt_static_img/report-logo.png',
                                    title: '永和大王·现磨豆浆·卤肉饭',
                                    content: [
                                        {
                                            label: 'ID',
                                            value: '27891901',
                                        },
                                        {
                                            value: '距离120m',
                                        },
                                    ],
                                    actionButtonText: '签入',
                                    checkInTimestamp: undefined,
                                },
                                {
                                    avatar: 'https://s3plus.meituan.net/readata/sagacious_pigeon/ppt_static_img/report-logo.png',
                                    title: '卷饼网·炸串',
                                    content: [
                                        {
                                            label: 'ID',
                                            value: '27891901',
                                        },
                                        {
                                            value: '距离120m',
                                        },
                                    ],
                                    actionButtonText: '签入',
                                    checkInTimestamp: undefined,
                                },
                                {
                                    avatar: 'https://s3plus.meituan.net/readata/sagacious_pigeon/ppt_static_img/report-logo.png',
                                    title: '安徽正宗牛肉板面',
                                    content: [
                                        {
                                            label: 'ID',
                                            value: '27891901',
                                        },
                                        {
                                            value: '距离120m',
                                        },
                                    ],
                                    actionButtonText: '签入',
                                    checkInTimestamp: undefined,
                                },
                                {
                                    avatar: 'https://s3plus.meituan.net/readata/sagacious_pigeon/ppt_static_img/report-logo.png',
                                    title: '乐鸭来北京烤鸭',
                                    content: [
                                        {
                                            label: 'ID',
                                            value: '27891901',
                                        },
                                        {
                                            value: '距离120m',
                                        },
                                    ],
                                    actionButtonText: '签入',
                                    checkInTimestamp: undefined,
                                },
                                {
                                    avatar: 'https://s3plus.meituan.net/readata/sagacious_pigeon/ppt_static_img/report-logo.png',
                                    title: '老马兰州牛肉拉面',
                                    content: [
                                        {
                                            label: 'ID',
                                            value: '27891901',
                                        },
                                        {
                                            value: '距离120m',
                                        },
                                    ],
                                    actionButtonText: '签入',
                                    actionButtonDisabled: true,
                                    // checkInTimestamp: undefined,
                                    checkInTimestamp:
                                        Date.now() - 30 * 60 * 1000, // 30分钟前签入
                                },
                            ],
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752052430689,
            tags: null,
        },
    },
    configForm: {
        code: 0,
        data: {
            questionMsgId: '6000001',
            msgId: '6000002',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752052430689,
            tags: null,
            currentContent: [
                {
                    type: 'configForm',
                    insert: {
                        configForm: {
                            config: [
                                {
                                    label: '拜访配置',
                                    type: 'switch',
                                    defaultValue: 'true',
                                },
                                {
                                    label: '提醒频次',
                                    type: 'select',
                                    options: ['每天', '每周', '每月', '从不'],
                                    defaultValue: '每周',
                                },
                                {
                                    label: '提醒时间',
                                    type: 'timeRangePicker',
                                    defaultValue: '13:00-18:00',
                                },
                            ],
                            formId: 'signReminder',
                            buttonText: '保存',
                        },
                    },
                },
            ],
        },
    },
};
