import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Modal,
    ScrollView,
    Dimensions,
} from '@mrn/react-native';
import { Icon, Switch, SlideModal, Datepicker } from '@roo/roo-rn';
import React, { useContext, useEffect, useState, useRef } from 'react';

import { useSendMessage } from '../../../hooks/useSendMessage';
import { EntryPoint } from '../../../types';

import Condition from '@/components/Condition/Condition';
import { AnswerContext } from '@/components/MessageBox/Answer/AnswerContext';

interface ConfigFormProps {
    config: {
        label: string;
        labelStyle?: 'bold'; // 标签样式
        type: 'switch' | 'select' | 'timeRangePicker';
        options?: string[]; // select需要，switch和timeRangePicker不需要
        defaultValue?: string; // 默认值 'true' | 'false'，'13:00-18:00'
    }[];
    formId: string; // 前端不使用，触发提问时透传给后端
    buttonText?: string; // 按钮文案，默认为确定
    history?: boolean; // 是否是历史消息
}

// Switch组件
const SwitchField: React.FC<{
    label: string;
    labelStyle?: 'bold';
    value: boolean;
    onChange: (value: boolean) => void;
    disabled?: boolean;
}> = ({ label, labelStyle, value, onChange, disabled }) => {
    return (
        <View style={styles.formItem}>
            <Text
                style={[
                    styles.label,
                    labelStyle === 'bold' && styles.labelBold,
                ]}
            >
                {label}
            </Text>
            <Switch
                value={value}
                onChange={onChange}
                disabled={disabled}
                backgroundColor='#EFEDFF'
                backgroundActiveColor='#4021FF'
            />
        </View>
    );
};

// Select组件 - 使用ActionSheet风格的Modal
const SelectField: React.FC<{
    label: string;
    labelStyle?: 'bold';
    options: string[];
    value: string;
    onChange: (value: string) => void;
    disabled?: boolean;
}> = ({ label, labelStyle, options, value, onChange, disabled }) => {
    const [isModalVisible, setIsModalVisible] = useState(false);

    const handleSelect = (option: string) => {
        onChange(option);
        setIsModalVisible(false);
    };

    return (
        <View style={styles.formItem}>
            <Text
                style={[
                    styles.label,
                    labelStyle === 'bold' && styles.labelBold,
                ]}
            >
                {label}
            </Text>
            <TouchableOpacity
                style={[
                    styles.selectButton,
                    disabled && styles.selectButtonDisabled,
                ]}
                onPress={() => !disabled && setIsModalVisible(true)}
                disabled={disabled}
            >
                <Text
                    style={[
                        styles.selectText,
                        disabled && styles.selectTextDisabled,
                    ]}
                >
                    {value || '请选择'}
                </Text>
                <Icon
                    type="expand-more"
                    size={16}
                    tintColor={disabled ? '#999999' : '#666666'}
                />
            </TouchableOpacity>

            {/* ActionSheet风格的Modal */}
            <Modal
                visible={isModalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setIsModalVisible(false)}
            >
                <TouchableOpacity
                    style={styles.modalOverlay}
                    activeOpacity={1}
                    onPress={() => setIsModalVisible(false)}
                >
                    <View style={styles.actionSheetContainer}>
                        <View style={styles.actionSheetHeader}>
                            <Text style={styles.actionSheetTitle}>{label}</Text>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => setIsModalVisible(false)}
                            >
                                <Icon type="close" size={20} tintColor="#666666" />
                            </TouchableOpacity>
                        </View>
                        <ScrollView style={styles.optionsScrollView}>
                            {options.map((option, index) => (
                                <TouchableOpacity
                                    key={index}
                                    style={[
                                        styles.actionSheetOption,
                                        option === value && styles.actionSheetOptionSelected,
                                    ]}
                                    onPress={() => handleSelect(option)}
                                >
                                    <Text
                                        style={[
                                            styles.actionSheetOptionText,
                                            option === value && styles.actionSheetOptionTextSelected,
                                        ]}
                                    >
                                        {option}
                                    </Text>
                                    {option === value && (
                                        <Icon type="check" size={16} tintColor="#6C5CE7" />
                                    )}
                                </TouchableOpacity>
                            ))}
                        </ScrollView>
                    </View>
                </TouchableOpacity>
            </Modal>
        </View>
    );
};

// TimeRangePicker组件 - 使用roo-rn的Datepicker和SlideModal
const TimeRangePickerField: React.FC<{
    label: string;
    labelStyle?: 'bold';
    value: string;
    onChange: (value: string) => void;
    disabled?: boolean;
}> = ({ label, labelStyle, value, onChange, disabled }) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [startTime, setStartTime] = useState(new Date());
    const [endTime, setEndTime] = useState(new Date());
    const [currentPicker, setCurrentPicker] = useState<'start' | 'end'>('start');
    const startPickerRef = useRef<any>(null);
    const endPickerRef = useRef<any>(null);

    // 解析当前值
    React.useEffect(() => {
        if (value && value.includes('-')) {
            const [start, end] = value.split('-');
            const [startHour, startMinute] = start.split(':').map(Number);
            const [endHour, endMinute] = end.split(':').map(Number);

            const startDate = new Date();
            startDate.setHours(startHour, startMinute, 0, 0);
            setStartTime(startDate);

            const endDate = new Date();
            endDate.setHours(endHour, endMinute, 0, 0);
            setEndTime(endDate);
        } else {
            // 默认时间范围 09:00-18:00
            const defaultStart = new Date();
            defaultStart.setHours(9, 0, 0, 0);
            setStartTime(defaultStart);

            const defaultEnd = new Date();
            defaultEnd.setHours(18, 0, 0, 0);
            setEndTime(defaultEnd);
        }
    }, [value]);

    const formatTime = (date: Date) => {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    };

    const handleConfirm = () => {
        const startTimeStr = formatTime(startTime);
        const endTimeStr = formatTime(endTime);
        const newValue = `${startTimeStr}-${endTimeStr}`;
        onChange(newValue);
        setIsModalVisible(false);
    };

    const handleStartTimeChange = (data: any) => {
        if (startPickerRef.current) {
            const selectedDate = startPickerRef.current.getSelectedDate();
            setStartTime(selectedDate);
        }
    };

    const handleEndTimeChange = (data: any) => {
        if (endPickerRef.current) {
            const selectedDate = endPickerRef.current.getSelectedDate();
            setEndTime(selectedDate);
        }
    };

    return (
        <View style={styles.formItem}>
            <Text
                style={[
                    styles.label,
                    labelStyle === 'bold' && styles.labelBold,
                ]}
            >
                {label}
            </Text>
            <TouchableOpacity
                style={[
                    styles.timeRangeButton,
                    disabled && styles.timeRangeButtonDisabled,
                ]}
                disabled={disabled}
                onPress={() => !disabled && setIsModalVisible(true)}
            >
                <Text
                    style={[
                        styles.timeRangeText,
                        disabled && styles.timeRangeTextDisabled,
                    ]}
                >
                    {value || '请选择时间范围'}
                </Text>
                <Icon
                    type="expand-more"
                    size={16}
                    tintColor={disabled ? '#999999' : '#666666'}
                />
            </TouchableOpacity>

            {/* 使用roo-rn的SlideModal */}
            <SlideModal
                visible={isModalVisible}
                title={label}
                leftLabel="取消"
                rightLabel="确定"
                leftCallback={() => setIsModalVisible(false)}
                rightCallback={handleConfirm}
            >
                <View style={styles.timePickerContent}>
                    <View style={styles.timePickerTabs}>
                        <TouchableOpacity
                            style={[
                                styles.timePickerTab,
                                currentPicker === 'start' && styles.timePickerTabActive,
                            ]}
                            onPress={() => setCurrentPicker('start')}
                        >
                            <Text
                                style={[
                                    styles.timePickerTabText,
                                    currentPicker === 'start' && styles.timePickerTabTextActive,
                                ]}
                            >
                                开始时间: {formatTime(startTime)}
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.timePickerTab,
                                currentPicker === 'end' && styles.timePickerTabActive,
                            ]}
                            onPress={() => setCurrentPicker('end')}
                        >
                            <Text
                                style={[
                                    styles.timePickerTabText,
                                    currentPicker === 'end' && styles.timePickerTabTextActive,
                                ]}
                            >
                                结束时间: {formatTime(endTime)}
                            </Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.datePickerContainer}>
                        {currentPicker === 'start' ? (
                            <Datepicker
                                ref={startPickerRef}
                                mode="time"
                                use12Hours={false}
                                proportion={[1, 1]}
                                date={startTime}
                                onChange={handleStartTimeChange}
                            />
                        ) : (
                            <Datepicker
                                ref={endPickerRef}
                                mode="time"
                                use12Hours={false}
                                proportion={[1, 1]}
                                date={endTime}
                                onChange={handleEndTimeChange}
                            />
                        )}
                    </View>
                </View>
            </SlideModal>
        </View>
    );
};

export const ConfigForm: React.FC<ConfigFormProps> = ({
    config,
    formId,
    buttonText = '保存',
    history: _history,
}) => {
    const [values, setValues] = useState<Record<string, string>>(() => {
        const initialValues: Record<string, string> = {};
        config.forEach((item) => {
            initialValues[item.label] = item.defaultValue || '';
        });
        return initialValues;
    });
    const [isSubmitted, setIsSubmitted] = useState(false);

    const { setWithForm } = useContext(AnswerContext);
    useEffect(() => {
        setWithForm(true);
    }, []);

    const { send } = useSendMessage();

    const handleSubmit = () => {
        setIsSubmitted(true);

        // 构造JSON格式的数据
        const formData = {
            formType: formId,
            ...values,
        };

        // 发送hideSpan类型的消息
        send(JSON.stringify(formData), undefined, EntryPoint.form_input);
    };

    const handleChange = (label: string, value: string) => {
        setValues((prev) => ({
            ...prev,
            [label]: value,
        }));
    };

    return (
        <View style={styles.container}>
            {config.map((item, index) => {
                const fieldValue = values[item.label];
                const isDisabled = isSubmitted;

                switch (item.type) {
                    case 'switch':
                        return (
                            <SwitchField
                                key={index}
                                label={item.label}
                                labelStyle={item.labelStyle}
                                value={fieldValue === 'true'}
                                onChange={(value) =>
                                    handleChange(item.label, value.toString())
                                }
                                disabled={isDisabled}
                            />
                        );
                    case 'select':
                        return (
                            <SelectField
                                key={index}
                                label={item.label}
                                labelStyle={item.labelStyle}
                                options={item.options || []}
                                value={fieldValue}
                                onChange={(value) =>
                                    handleChange(item.label, value)
                                }
                                disabled={isDisabled}
                            />
                        );
                    case 'timeRangePicker':
                        return (
                            <TimeRangePickerField
                                key={index}
                                label={item.label}
                                labelStyle={item.labelStyle}
                                value={fieldValue}
                                onChange={(value) =>
                                    handleChange(item.label, value)
                                }
                                disabled={isDisabled}
                            />
                        );
                    default:
                        return null;
                }
            })}

            <Condition condition={[!isSubmitted]}>
                <TouchableOpacity
                    style={styles.submitButton}
                    onPress={handleSubmit}
                >
                    <Text style={styles.submitButtonText}>{buttonText}</Text>
                </TouchableOpacity>
            </Condition>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 8,
        padding: 16,
        backgroundColor: '#FFFFFF',
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#222222',
        marginBottom: 20,
    },
    formItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 20,
        minHeight: 44,
    },
    label: {
        fontSize: 16,
        color: '#222222',
        flex: 1,
    },
    labelBold: {
        fontWeight: 'bold',
    },
    selectButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#F5F6FA',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 12,
        minWidth: 120,
        borderWidth: 1,
        borderColor: 'transparent',
    },
    selectButtonDisabled: {
        opacity: 0.5,
    },
    selectText: {
        fontSize: 16,
        color: '#222222',
        marginRight: 8,
    },
    selectTextDisabled: {
        color: '#999999',
    },
    // Modal样式
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
    },
    // ActionSheet样式
    actionSheetContainer: {
        backgroundColor: '#FFFFFF',
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        maxHeight: Dimensions.get('window').height * 0.6,
    },
    actionSheetHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    actionSheetTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#222222',
    },
    closeButton: {
        padding: 4,
    },
    optionsScrollView: {
        maxHeight: 300,
    },
    actionSheetOption: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    actionSheetOptionSelected: {
        backgroundColor: '#F0F0FF',
    },
    actionSheetOptionText: {
        fontSize: 16,
        color: '#222222',
    },
    actionSheetOptionTextSelected: {
        color: '#6C5CE7',
        fontWeight: '500',
    },
    timeRangeButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#F5F6FA',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 12,
        minWidth: 140,
        borderWidth: 1,
        borderColor: 'transparent',
    },
    timeRangeButtonDisabled: {
        opacity: 0.5,
    },
    timeRangeText: {
        fontSize: 16,
        color: '#222222',
        marginRight: 8,
    },
    timeRangeTextDisabled: {
        color: '#999999',
    },
    // 时间选择器样式 - 使用roo-rn组件
    timePickerContent: {
        backgroundColor: '#FFFFFF',
        paddingVertical: 16,
    },
    timePickerTabs: {
        flexDirection: 'row',
        marginBottom: 16,
        paddingHorizontal: 16,
    },
    timePickerTab: {
        flex: 1,
        paddingVertical: 12,
        paddingHorizontal: 16,
        backgroundColor: '#F5F6FA',
        borderRadius: 8,
        marginHorizontal: 4,
        alignItems: 'center',
    },
    timePickerTabActive: {
        backgroundColor: '#6C5CE7',
    },
    timePickerTabText: {
        fontSize: 14,
        color: '#666666',
        fontWeight: '500',
    },
    timePickerTabTextActive: {
        color: '#FFFFFF',
    },
    datePickerContainer: {
        backgroundColor: '#FFFFFF',
        minHeight: 200,
    },
    submitButton: {
        backgroundColor: '#6C5CE7',
        borderRadius: 24,
        paddingVertical: 16,
        alignItems: 'center',
        marginTop: 16,
        marginHorizontal: 0,
    },
    submitButtonText: {
        fontSize: 16,
        color: '#FFFFFF',
        fontWeight: '500',
    },
});
