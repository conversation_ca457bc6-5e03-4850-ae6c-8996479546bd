import React, { useState, useEffect, useRef } from 'react';

import TaskListButton from './TaskListButton';
import TaskListDrawer from './TaskListDrawer';
import { getTaskStatus, TaskStatusResponse } from '../../api/taskApi';
import { useTaskStore } from '../../store/task';

interface TaskListManagerProps {
    /** 发送消息回调 */
    onSendMessage: (content: string) => void;
}

const TaskListManager: React.FC<TaskListManagerProps> = ({ onSendMessage }) => {
    const [taskStatus, setTaskStatus] = useState<TaskStatusResponse | null>(
        null,
    );
    const { isDrawerVisible, openDrawer, closeDrawer } = useTaskStore();
    const [loading, setLoading] = useState(false);
    const pollingRef = useRef<NodeJS.Timeout | null>(null);

    // 获取任务状态
    const fetchTaskStatus = async () => {
        try {
            const data = await getTaskStatus();
            setTaskStatus(data);
        } catch (error) {
            console.error('获取任务状态失败:', error);
        }
    };

    // 开始轮询
    const startPolling = () => {
        // 立即执行一次
        fetchTaskStatus();

        // 设置2秒轮询
        pollingRef.current = setInterval(() => {
            fetchTaskStatus();
        }, 2000);
    };

    // 停止轮询
    const stopPolling = () => {
        if (pollingRef.current) {
            clearInterval(pollingRef.current);
            pollingRef.current = null;
        }
    };

    // 组件挂载时开始轮询
    useEffect(() => {
        startPolling();

        return () => {
            stopPolling();
        };
    }, []);

    // 处理按钮点击
    const handleButtonPress = () => {
        if (loading) {
            return;
        }

        setLoading(true);
        openDrawer();

        // 延迟重置loading状态
        setTimeout(() => {
            setLoading(false);
        }, 300);
    };

    // 处理抽屉关闭
    const handleDrawerClose = () => {
        closeDrawer();
    };

    // 处理发送消息
    const handleSendMessage = (content: string) => {
        onSendMessage(content);
    };

    const runningCount = taskStatus?.runnings || 0;
    const showCompleted = taskStatus?.showCompleted || false;

    return (
        <>
            <TaskListButton
                runningCount={runningCount}
                showCompleted={showCompleted}
                onPress={handleButtonPress}
                loading={loading}
            />
            <TaskListDrawer
                visible={isDrawerVisible}
                onClose={handleDrawerClose}
                onSendMessage={handleSendMessage}
            />
        </>
    );
};

export default TaskListManager;
