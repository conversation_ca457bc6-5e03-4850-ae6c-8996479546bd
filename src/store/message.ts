import { apiCaller } from '@mfe/cc-api-caller-bee';
import { Toast } from '@roo/roo-rn';
import dayjs from 'dayjs';
import { produce } from 'immer';
import _ from 'lodash';
import { create } from 'zustand';

import fileStore from './file';
import { FileStateAndActions } from './file';
import { AddMode, ClipboardData, MessagePollingReq } from './type';
import { UiStateAndActions } from './uiState';
import { VERSION_PARAMS_2 } from '../consts';
import {
    AbilityType,
    Message,
    MessageContentType,
    MessageSendReq,
    MessageStatus,
    MessageType,
} from '../types';
import isEmptyAnswer from '../utils/isEmptyAnswer';
import { trackEvent } from '../utils/track';

// import { AdditionFile, AdditionImage } from '@/types/message';
export const HISTORY_SYS_MESSAGE_ID = 'HISTORY_SYS_MESSAGE_ID';
export const REFRESH_SYS_MESSAGE_ID = 'REFRESH_SYS_MESSAGE_ID';

export const sanitize = <T>(one: T, standardGenerator: (p?: any) => T) => {
    const standard = standardGenerator();

    return Object.keys({ ...standard, ...one }).reduce((obj, key) => {
        // @ts-ignore
        obj[key] = obj[key] == null ? standard[key] : obj[key];
        return obj;
    }, one || ({} as T));
};

export const createDefaultMessage = (
    p: { msgId?: string; questionMsgId?: string; status?: MessageStatus } = {},
) =>
    ({
        status: MessageStatus.TO_GENERATE,
        msgId: p.msgId || p.questionMsgId || `${Math.random()}`,
        questionMsgId: p.questionMsgId || `${Math.random()}`,
        abilityType: undefined,
        subAbilityType: undefined,
        msgType: MessageContentType.TEXT,
        type: MessageType.ANSWER,
        currentContent: '',
        previousContent: [],
        prefixTextContent: '',
        postTextContent: '',
        selectionItems: [],
    } as Message);

const MAX_RETRY_COUNT = 5;

// type AdditionMedia = AdditionImage | AdditionFile;
const defaultState = {
    isPollingMessage: false,
    isLoadingHistory: false,
    hasHistory: false,
    minMsgId: '',
    messageList: [] as Message[],
    historyMessageList: [] as Message[],
    sessionId: [] as string[],
    clipboardData: {} as ClipboardData,
    scrollToEnd: (() => {}) as (top?: boolean) => void,
    delayScrollTimeout: null as NodeJS.Timeout | null,
    pollingTimer: null as NodeJS.Timeout | null,
};
type State = typeof defaultState & {
    input: {
        text: string;
        changeEvent: ((text: string) => void)[];
        blur: () => void;
        focus: () => void;
        send: () => void;
        addChangeEvent: (cb: (text: string) => void) => void;
        clear: () => void;
        set: (text: string) => void;
        setInputState: (inputState: {
            text: string;
            changeEvent: ((text: string) => void)[];
            blur: () => void;
            focus: () => void;
            send: () => void;
        }) => void;
    };
};

const getActions = (set: Setter, get: Getter, uiState: UiStateAndActions) => ({
    getSessionId: () => get().sessionId,
    setClipboardData: (data: ClipboardData) => {
        set(
            produce<State>((state) => {
                state.clipboardData = data;
            }),
        );
    },
    setIsPollingMessage: (isPollingMessage: boolean) => {
        set(
            produce<State>((state) => {
                state.isPollingMessage = isPollingMessage;
            }),
        );
    },
    getClipboardData: () => get().clipboardData,
    refreshSession: () => {
        const add = get().add;
        const disconnect = get().disconnect;
        const scrollToEnd = get().scrollToEnd;
        const appendSessionId = get().appendSessionId;

        return async (noRefreshMsg = false, callback?) => {
            trackEvent('chat_refresh');

            !noRefreshMsg && disconnect();
            const res = await apiCaller.send(
                '/bee/v1/bdaiassistant/refreshSession',
                { sessionId: get().getLatestSessionId() },
            );

            // 刷新会话时强制滚动
            scrollToEnd(true);

            if (res.code !== 0) {
                return;
            }

            appendSessionId(res.data.sessionId);
            callback?.();

            if (!res.data.showRefreshMsg || noRefreshMsg) {
                return;
            }

            Toast.open('已为您生成新的对话 ~ ');
            add({
                status: MessageStatus.DONE,
                type: MessageType.SYSTEM,
                msgId: `${REFRESH_SYS_MESSAGE_ID}_${dayjs().unix()}`,
                abilityType: undefined,
                subAbilityType: undefined,
                msgType: MessageContentType.TEXT,
                currentContent: '问我新的问题吧',
            });
        };
    },
    getLatestSessionId: () => {
        return _.last(get().sessionId);
    },
    appendSessionId: (id) => {
        set(
            produce<State>((state) => {
                state.sessionId.push(id);
            }),
        );
    },
    delayScrollTimeout: null as any,
    delayScrollToEnd: Object.assign(
        (delay = 200, force = false) => {
            if (get().delayScrollTimeout) {
                clearTimeout(get().delayScrollTimeout);
            }
            const timeout = setTimeout(() => get().scrollToEnd(force), delay);
            set(
                produce<State>((state) => {
                    state.delayScrollTimeout = timeout;
                }),
            );
            return timeout;
        },
        { _persist: true },
    ),
    setScrollToEnd: (scrollToEnd) => {
        set(
            produce<State>((state) => {
                state.scrollToEnd = scrollToEnd;
            }),
        );
    },
    add: (m, mode = AddMode.APPEND) => {
        set(
            produce<State>((state) => {
                if (mode === AddMode.APPEND) {
                    state.messageList.push(m);
                } else {
                    state.messageList.unshift(m);
                }
            }),
        );
    },
    send: (message, bizId, entryPointType, entryPoint) => {
        if (!message) {
            return;
        }
        if (typeof message === 'string') {
            // referer参数暂时不需要
            // @ts-ignore
            get().sendMessage({
                content: message,
                abilityType: AbilityType.GENERAL,
                bizId,
                entryPointType,
                entryPoint,
            });
            return;
        }

        get().sendMessage({
            entryPointType,
            bizId,
            entryPoint,
            ...message,
        });
    },
    setHasHistory: (hasHistory: boolean) => {
        set(
            produce<State>((state) => {
                state.hasHistory = hasHistory;
            }),
        );
    },
    setTyping: (msgId: string, typing: boolean) => {
        set(
            produce<State>((state) => {
                const item = state.messageList.find((it) => it.msgId === msgId);

                if (!item) {
                    return;
                }

                item.status = typing
                    ? MessageStatus.TYPING
                    : item.lastStatus == null
                    ? MessageStatus.DONE
                    : item.lastStatus;
            }),
        );
    },
    mutateMsg: (
        msgId: string,
        payload: Partial<Message> | ((msg: Message) => Partial<Message>),
    ) => {
        set(
            produce<State>((state) => {
                const index = state.messageList.findIndex(
                    (it) => it.msgId === msgId,
                );

                if (index === -1) {
                    return;
                }
                let finalPayload = payload;
                if (typeof payload === 'function') {
                    const msg = state.messageList[index];
                    finalPayload = payload(msg);
                }
                Object.keys(finalPayload).forEach(
                    (key) =>
                        (state.messageList[index][key] = finalPayload[key]),
                );
            }),
        );
    },
    retrySend: async (id: string) => {
        if (get().isPollingMessage) {
            return Toast.open('正在回答，请稍等');
        }
        let data;
        set(
            produce<State>((state) => {
                const index = state.messageList.findIndex(
                    (it) => it.msgId === id,
                );
                const msg = state.messageList[index];
                state.messageList[index] = {
                    ...msg,
                    status: MessageStatus.GENERATING,
                };
                data = {
                    ...msg.req,
                    version: 'V3',
                    sessionId: get().getLatestSessionId(),
                };
            }),
        );
        const request = async () =>
            await apiCaller.post('/bee/v1/bdaiassistant/submitQuery', data);
        let res = await request();
        if (res.code === 1) {
            get().refreshSession()(true);
            res = await request();
        }

        if (res.code !== 0) {
            get().mutateMsg(id, { status: MessageStatus.ERROR });
            set(
                produce<State>((state) => {
                    state.isPollingMessage = false;
                }),
            );
            return;
        }
        get().mutateMsg(id, { status: MessageStatus.DONE });
        set(
            produce<State>((state) => {
                state.messageList.push(
                    createDefaultMessage({
                        // @ts-ignore
                        questionMsgId: res.data.questionMsgId,
                    }),
                );
            }),
        );
        get().pollingMessage(res.data);
    },
    sendMessage: async (data: MessageSendReq) => {
        if (get().isPollingMessage) {
            return Toast.open('正在回答，请稍等');
        }
        set(
            produce<State>((state) => {
                state.isPollingMessage = true;
            }),
        );
        const timestamp = String(dayjs().valueOf());
        set(
            produce<State>((state) => {
                const { abilityType, subAbilityType, content } = data;
                state.messageList.push({
                    abilityType,
                    subAbilityType,
                    msgId: timestamp,
                    type: MessageType.QUESTION,
                    status: MessageStatus.DONE,
                    msgType: MessageContentType.TEXT,
                    currentContent: content,
                    req: data,
                    entryPointType: data.entryPointType,
                    isQuestion: true,
                });
            }),
        );

        get().delayScrollToEnd();
        const timeout = setTimeout(() => {
            get().mutateMsg(timestamp, {
                status: MessageStatus.GENERATING, // 2s后展示loading状态
            });
        }, 2000);

        const submitQuery = async () => {
            const params = {
                ...data,
                sessionId: get().getLatestSessionId(),
                version: 'V3',
            };
            if (params.entryPoint && params.entryPointType) {
                delete params.entryPointType;
            }
            return await apiCaller.post(
                '/bee/v1/bdaiassistant/submitQuery',
                params,
                { silent: true },
            );
        };

        let res = await submitQuery();
        if (res.code === 1) {
            await get().refreshSession()(true);
            res = await submitQuery();
        }

        clearTimeout(timeout);

        if (res.code !== 0) {
            get().mutateMsg(timestamp, { status: MessageStatus.ERROR });
            set(
                produce<State>((state) => {
                    state.isPollingMessage = false;
                }),
            );
            return;
        }

        set(
            produce<State>((state) => {
                state.messageList.push(
                    createDefaultMessage({
                        // @ts-ignore
                        questionMsgId: res.data.questionMsgId,
                        status: MessageStatus.TYPING,
                    }),
                );
                state.isPollingMessage = false;
            }),
        );
        set(
            produce<State>((state) => {
                state.isPollingMessage = true;
            }),
        );
        get().pollingMessage(res.data);
    },
    checkIsPolling: () => {
        if (get().isPollingMessage) {
            Toast.open('正在回答，请稍等');
        }
        return get().isPollingMessage;
    },
    pollingMessage: async (params: MessagePollingReq, retryCount = 0) => {
        const endPolling = () => {
            set(
                produce<State>((state) => {
                    state.isPollingMessage = false;
                }),
            );
        };
        const current = get().messageList.find(
            (it) =>
                it.msgId === params.msgId ||
                it.questionMsgId === params.questionMsgId,
        );

        // 如果这个消息已经被用户手动停止, 则不再更新这个数据
        if (
            [MessageStatus.STOPPED, MessageStatus.DONE_AFTER_STOP].includes(
                current?.status,
            ) ||
            !get().isPollingMessage
        ) {
            endPolling();
            return;
        }

        const fetchAnswer = async () =>
            await apiCaller.post(
                '/bee/v1/bdaiassistant/fetchAnswer',
                {
                    ...params,
                    ...VERSION_PARAMS_2,
                    sessionId: get().getLatestSessionId(),
                },
                {
                    silent: retryCount !== MAX_RETRY_COUNT,
                }, // 只有在重试超限仍然失败后，才提示错误
            );
        let res = await fetchAnswer();
        if (res.code === 1) {
            await get().refreshSession()(true);
            res = await fetchAnswer();
        }

        if (res.code !== 0) {
            // 失败需要继续轮询
            if (retryCount < MAX_RETRY_COUNT) {
                get().pollingMessage(params, ++retryCount);
                endPolling();
                return;
            }

            // 轮询一直失败的话，需要给错误
            set(
                produce<State>((state) => {
                    const index = state.messageList.findIndex(
                        (it) =>
                            it.msgId === params.msgId ||
                            it.questionMsgId === params.questionMsgId,
                    );

                    // 没找到这个任务的话，就填充一个默认值
                    if (index === -1) {
                        state.messageList.push({
                            ...createDefaultMessage(params),
                            currentContent:
                                '非常抱歉服务器去送外卖了，再问一句试试呀',
                            status: MessageStatus.ERROR,
                        });
                        return;
                    }

                    state.messageList[index].status = MessageStatus.ERROR;
                    state.messageList[index].currentContent =
                        '非常抱歉服务器去送外卖了，再问一句试试呀';
                }),
            );
            endPolling();
            return;
        }

        const tags = res.data?.tags as (
            | 'poiId'
            | 'panelOpen'
            | 'poiDiagnoseSelector'
            | 'poiMultiSelector'
        )[];
        // poiDiagnoseSelector(商家诊断专用)
        if (
            tags?.length &&
            (tags.includes('poiId') ||
                tags.includes('poiDiagnoseSelector') ||
                tags.includes('poiMultiSelector'))
        ) {
            uiState?.setPoiSelectorOpen(true);
        }
        if (tags?.length && tags.includes('panelOpen')) {
            uiState?.setPanelOpen(true);
        }

        const resMsgId = res.data.msgId;

        const resMsg: Message = sanitize(
            res.data as unknown as Message,
            createDefaultMessage,
        );

        if (resMsg.status === MessageStatus.DONE && isEmptyAnswer(resMsg)) {
            resMsg.currentContent =
                '抱歉，因服务过于火爆，暂时无法给出答案，建议老铁稍后重新咨询!';
        }

        const resCurrentContent = resMsg.currentContent;

        // 不为空字符串
        if (resCurrentContent) {
            let resCurrentContentObj = [];
            try {
                resCurrentContentObj =
                    typeof resCurrentContent === 'string'
                        ? JSON.parse(resCurrentContent)
                        : resCurrentContent;
            } catch (e) {}

            if (resCurrentContentObj.length) {
                set(
                    produce<State>((state) => {
                        const index = state.messageList.findIndex(
                            (it) =>
                                it.msgId === params.msgId ||
                                it.questionMsgId === params.questionMsgId,
                        );
                        const current = state.messageList[index];

                        let status = resMsg.status;

                        // 消息过来还有内容，或者正在输入的，不改变输入中的状态
                        if (
                            !resMsg.sensitive &&
                            (resMsg.currentContent ||
                                current.status === MessageStatus.TYPING)
                        ) {
                            resMsg.lastStatus = status;
                            status = MessageStatus.TYPING;
                        }

                        // 缓存数据，FlatList刷新后会丢失数据
                        let draftCurrentContent = [];
                        try {
                            draftCurrentContent = [
                                ...(current.currentContent || []),
                                ...resCurrentContentObj,
                            ];
                        } catch (e) {
                            draftCurrentContent = current.currentContent;
                        }
                        state.messageList[index] = {
                            ...resMsg,
                            status,
                            previousContent: draftCurrentContent,
                            currentContent: draftCurrentContent,
                        };
                    }),
                );
                get().delayScrollToEnd();
            }
        }

        const timeout = setTimeout(() => {
            // 消息完成，则不再发起轮询
            if (
                [
                    MessageStatus.DONE,
                    MessageStatus.DONE_AFTER_STOP,
                    MessageStatus.STOPPED,
                ].includes(resMsg.status)
            ) {
                endPolling();
                return;
            }
            // 继续轮询
            get().pollingMessage({ ...params, msgId: resMsgId }, 0);
        }, 500);
        set(
            produce<State>((state) => {
                if (state.pollingTimer) {
                    clearTimeout(state.pollingTimer);
                }
                state.pollingTimer = timeout;
            }),
        );
    },
    stopPolling: (msgId?: string) => {
        let latestMsgId = msgId;
        if (!latestMsgId) {
            const messageList = get().messageList.filter((it) =>
                [
                    MessageStatus.TO_GENERATE,
                    MessageStatus.GENERATING,
                    MessageStatus.TYPING,
                ].includes(it.status),
            );
            const latestMsg = messageList[messageList.length - 1];
            if (!latestMsg) {
                return;
            }
            latestMsgId = latestMsg.msgId || latestMsg.questionMsgId;
        }
        if (get().pollingTimer) {
            clearTimeout(get().pollingTimer);
        }
        set(
            produce<State>((state) => {
                state.isPollingMessage = false;
            }),
        );
        get().mutateMsg(latestMsgId, (msg) => ({
            status: MessageStatus.STOPPED,
            currentContent: msg.currentContent,
        }));
    },

    disconnect: () => {
        const onGoingMsgs = get().messageList.filter(
            (m) =>
                m.status === MessageStatus.TO_GENERATE ||
                m.status === MessageStatus.GENERATING ||
                m.status === MessageStatus.TYPING,
        );

        onGoingMsgs.map((m) => get().stopPolling(m.msgId));
    },
    getHistoryMessageList: async () => {
        if (!get().hasHistory) {
            return;
        }
        set(
            produce<State>((state) => {
                state.isLoadingHistory = true;
            }),
        );
        const getHistory = async () =>
            await apiCaller.post(
                '/bee/v1/bdaiassistant/getChatHistory',
                {
                    minMsgId: get().minMsgId,
                    ...VERSION_PARAMS_2,
                    sessionId: get().getLatestSessionId(),
                },
                { silent: true },
            );
        let res = await getHistory();
        if (res.code === 1) {
            await get().refreshSession()(true);
            res = await getHistory();
        }
        if (res.code !== 0) {
            get().setHistoryLoading(false);
            return;
        }

        if (!res.data || !res.data.msgItems || !res.data.msgItems.length) {
            get().setHasHistory(false);
            set(
                produce<State>((state) => {
                    state.historyMessageList.unshift({
                        status: MessageStatus.DONE,
                        type: MessageType.SYSTEM,
                        msgId: `${Math.random()}`,
                        abilityType: undefined,
                        subAbilityType: undefined,
                        msgType: MessageContentType.TEXT,
                        currentContent: '没有更多啦',
                    });
                }),
            );

            return;
        }
        set(
            produce<State>((state) => {
                // @ts-ignore 非0情况已提前返回
                const withHistory = res.data.msgItems
                    .map((it) => ({
                        ...it,
                        history: true,
                    }))
                    .map((d) =>
                        sanitize(d as unknown as Message, createDefaultMessage),
                    );
                state.isLoadingHistory = true;
                // @ts-ignore 非0已提前返回
                state.minMsgId = res.data.minMsgId;
                state.historyMessageList = [
                    ...withHistory,
                    ...state.historyMessageList,
                ];

                const historySysMsg = state.messageList.find((msg) =>
                    msg.msgId.startsWith(HISTORY_SYS_MESSAGE_ID),
                );

                historySysMsg.currentContent = '以上为历史消息';
            }),
        );
    },
    setHistoryLoading: (isLoading: boolean) => {
        set(
            produce<State>((state) => {
                state.isLoadingHistory = isLoading;
            }),
        );
    },
    reset() {
        set(
            produce<State>((state) => {
                get().disconnect();
                state.messageList = [];
                state.historyMessageList = [];
                state.hasHistory = false;
                state.isLoadingHistory = false;
                state.minMsgId = undefined;
            }),
        );
    },
    input: {
        text: '',
        changeEvent: [],
        blur: () => {},
        focus: () => {},
        send: () => {},
        addChangeEvent: (cb) => {
            set(
                produce<State>((state) => {
                    state.input.changeEvent.push(cb);
                }),
            );
        },
        clear: () => {
            set(
                produce<State>((state) => {
                    state.input.text = '';
                }),
            );
        },
        set: (text: string) => {
            set(
                produce<State>((state) => {
                    state.input.text = text;
                }),
            );
        },
        setInputState: (inputState) => {
            set(
                produce<State>((state) => {
                    state.input = { ...state.input, ...inputState };
                }),
            );
        },
    },
});
type Actions = ReturnType<typeof getActions>;
export type StateAndActions = State & Actions & FileStateAndActions;
type Setter = (v: Partial<State> | ((state: State) => Partial<State>)) => void;
type Getter = () => StateAndActions;

export const createMessage = (uiState: UiStateAndActions) =>
    create<StateAndActions>((set: any, get) => ({
        ...defaultState,
        ...fileStore.defaultState,
        ...getActions(set, get, uiState),
        ...fileStore.getActions(set, get),
    }));
